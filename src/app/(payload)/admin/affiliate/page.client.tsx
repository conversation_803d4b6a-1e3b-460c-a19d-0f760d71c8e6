'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Gutter } from '@payloadcms/ui'
import type { User, AffiliateLink, AffiliateClickLog, AffiliateSetting } from '@/payload-types'
import DashboardTab from './components/DashboardTab'
import ManageAffiliateUserTab from './components/ManageAffiliateUserTab'
import AffiliateClickLogsTab from './components/AffiliateClickLogsTab'

interface Props {
  affiliateUsers: User[]
  recentClickLogs: AffiliateClickLog[]
  affiliateSettings: AffiliateSetting[]
  affiliateLinks: AffiliateLink[]
}

const AffiliateManagementClient: React.FC<Props> = ({
  affiliateUsers,
  recentClickLogs,
  affiliateSettings,
  affiliateLinks,
}) => {
  const [selectedAffiliateUser, setSelectedAffiliateUser] = useState<User | null>(null)

  return (
    <Gutter>
      <div className="py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Affiliate Management</h1>
          <p className="text-gray-600">
            Manage affiliate users, track performance, and configure affiliate settings
          </p>
        </div>

        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="dashboard" className="text-sm font-medium">
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="manage-users" className="text-sm font-medium">
              Manage Affiliate Users
            </TabsTrigger>
            <TabsTrigger value="click-logs" className="text-sm font-medium">
              Affiliate Click Logs
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <DashboardTab
              affiliateUsers={affiliateUsers}
              recentClickLogs={recentClickLogs}
              affiliateSettings={affiliateSettings}
              affiliateLinks={affiliateLinks}
            />
          </TabsContent>

          <TabsContent value="manage-users" className="space-y-6">
            <ManageAffiliateUserTab
              affiliateUsers={affiliateUsers}
              affiliateSettings={affiliateSettings}
              affiliateLinks={affiliateLinks}
              selectedAffiliateUser={selectedAffiliateUser}
              onSelectAffiliateUser={setSelectedAffiliateUser}
            />
          </TabsContent>

          <TabsContent value="click-logs" className="space-y-6">
            <AffiliateClickLogsTab
              clickLogs={recentClickLogs}
              affiliateUsers={affiliateUsers}
            />
          </TabsContent>
        </Tabs>
      </div>
    </Gutter>
  )
}

export default AffiliateManagementClient