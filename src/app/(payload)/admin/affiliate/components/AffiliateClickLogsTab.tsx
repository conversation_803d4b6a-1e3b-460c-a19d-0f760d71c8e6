'use client'

import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SelectInput } from '@payloadcms/ui'
import type { User, AffiliateClickLog } from '@/payload-types'
import { MousePointer, Search, Filter, Calendar, MapPin, Globe } from 'lucide-react'

interface Props {
  clickLogs: AffiliateClickLog[]
  affiliateUsers: User[]
}

const AffiliateClickLogsTab: React.FC<Props> = ({
  clickLogs,
  affiliateUsers,
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 20

  // Prepare user filter options
  const userFilterOptions = [
    { label: 'All Users', value: '' },
    ...affiliateUsers.map(user => ({
      label: `${user.email} ${user.firstName && user.lastName ? `(${user.firstName} ${user.lastName})` : ''}`,
      value: user.id.toString(),
    }))
  ]

  // Filter and search logs
  const filteredLogs = useMemo(() => {
    let filtered = clickLogs

    // Filter by selected user
    if (selectedUser) {
      filtered = filtered.filter(log => {
        if (typeof log.affiliateUser === 'object') {
          return log.affiliateUser?.id.toString() === selectedUser
        }
        return log.affiliateUser?.toString() === selectedUser
      })
    }

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(log => {
        const userEmail = typeof log.affiliateUser === 'object' 
          ? log.affiliateUser?.email?.toLowerCase() || ''
          : ''
        const ip = log.ip?.toLowerCase() || ''
        const location = log.location?.toLowerCase() || ''
        const referrer = log.referrer?.toLowerCase() || ''
        
        return userEmail.includes(term) || 
               ip.includes(term) || 
               location.includes(term) || 
               referrer.includes(term)
      })
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
  }, [clickLogs, selectedUser, searchTerm])

  // Pagination
  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage)
  const paginatedLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleUserFilterChange = (option: any) => {
    setSelectedUser(option?.value || '')
    setCurrentPage(1)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <div className="space-y-6">
      {/* Header and Stats */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MousePointer className="h-5 w-5" />
            Affiliate Click Logs
          </CardTitle>
          <CardDescription>
            Track and analyze affiliate link clicks across all users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{clickLogs.length}</div>
              <div className="text-sm text-blue-600">Total Clicks</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{filteredLogs.length}</div>
              <div className="text-sm text-green-600">Filtered Results</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {new Set(clickLogs.map(log => log.ip)).size}
              </div>
              <div className="text-sm text-purple-600">Unique IPs</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="field-type">
              <label className="field-label">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by email, IP, location, or referrer..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value)
                    setCurrentPage(1)
                  }}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="field-type">
              <label className="field-label">Filter by User</label>
              <SelectInput
                path="userFilter"
                options={userFilterOptions}
                value={selectedUser}
                onChange={handleUserFilterChange}
                placeholder="All Users"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Click Logs Table */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle>Click Logs</CardTitle>
          <CardDescription>
            Showing {paginatedLogs.length} of {filteredLogs.length} results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3 font-medium">User</th>
                  <th className="text-left p-3 font-medium">Date & Time</th>
                  <th className="text-left p-3 font-medium">IP Address</th>
                  <th className="text-left p-3 font-medium">Location</th>
                  <th className="text-left p-3 font-medium">Referrer</th>
                </tr>
              </thead>
              <tbody>
                {paginatedLogs.map((log) => (
                  <tr key={log.id} className="border-b hover:bg-gray-50">
                    <td className="p-3">
                      <div className="space-y-1">
                        <div className="font-medium">
                          {typeof log.affiliateUser === 'object' && log.affiliateUser?.email
                            ? log.affiliateUser.email
                            : 'Unknown User'}
                        </div>
                        {typeof log.affiliateUser === 'object' && 
                         log.affiliateUser?.firstName && 
                         log.affiliateUser?.lastName && (
                          <div className="text-xs text-gray-500">
                            {log.affiliateUser.firstName} {log.affiliateUser.lastName}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{formatDate(log.createdAt)}</span>
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-mono">{log.ip || 'N/A'}</span>
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{log.location || 'Unknown'}</span>
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="text-sm text-gray-600 max-w-xs truncate">
                        {log.referrer || 'Direct'}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {paginatedLogs.length === 0 && (
            <div className="text-center py-8">
              <MousePointer className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Click Logs Found</h3>
              <p className="text-gray-600">
                {filteredLogs.length === 0 && clickLogs.length > 0
                  ? 'Try adjusting your filters to see more results.'
                  : 'No affiliate link clicks have been recorded yet.'}
              </p>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default AffiliateClickLogsTab
