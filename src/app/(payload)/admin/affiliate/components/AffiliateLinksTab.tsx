'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import type { User, AffiliateLink } from '@/payload-types'
import { Link, Plus, Edit, Copy, ExternalLink, Eye, ToggleLeft, ToggleRight } from 'lucide-react'

interface Props {
  selectedUser: User
  userLinks: AffiliateLink[]
}

const AffiliateLinksTab: React.FC<Props> = ({
  selectedUser,
  userLinks,
}) => {
  const [expandedLink, setExpandedLink] = useState<number | null>(null)
  const [copiedLink, setCopiedLink] = useState<number | null>(null)

  const toggleExpanded = (linkId: number) => {
    setExpandedLink(expandedLink === linkId ? null : linkId)
  }

  const handleCopyLink = async (link: AffiliateLink) => {
    if (link.targetLink) {
      try {
        await navigator.clipboard.writeText(link.targetLink)
        setCopiedLink(link.id)
        setTimeout(() => setCopiedLink(null), 2000)
      } catch (err) {
        console.error('Failed to copy link:', err)
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getEventTitle = (link: AffiliateLink) => {
    if (typeof link.event === 'object' && link.event?.title) {
      return link.event.title
    }
    return 'General Link'
  }

  const getPromotionTitle = (link: AffiliateLink) => {
    if (typeof link.affiliatePromotion === 'object' && link.affiliatePromotion?.title) {
      return link.affiliatePromotion.title
    }
    return null
  }

  const getLinkStatus = (link: AffiliateLink) => {
    return link.status || 'active'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Affiliate Links</h3>
          <p className="text-sm text-gray-600">
            Manage affiliate links for {selectedUser.email}
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create New Link
        </Button>
      </div>

      {/* Links List */}
      {userLinks.length > 0 ? (
        <div className="space-y-4">
          {userLinks.map((link) => (
            <Card key={link.id} className="shadow-md">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Link className="h-5 w-5 text-gray-500" />
                    <div>
                      <CardTitle className="text-base">
                        {getEventTitle(link)}
                      </CardTitle>
                      <CardDescription>
                        Code: {link.affiliateCode} • Created: {formatDate(link.createdAt)}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getLinkStatus(link) === 'active' ? "default" : "secondary"}>
                      {getLinkStatus(link)}
                    </Badge>
                    {link.targetLink && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyLink(link)}
                      >
                        {copiedLink === link.id ? (
                          <span className="text-green-600">Copied!</span>
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(link.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {expandedLink === link.id && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Basic Info */}
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Affiliate Code</label>
                        <div className="text-sm font-mono bg-gray-100 p-2 rounded mt-1">
                          {link.affiliateCode}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status</label>
                        <div className="flex items-center gap-2 mt-1">
                          {getLinkStatus(link) === 'active' ? (
                            <ToggleRight className="h-5 w-5 text-green-500" />
                          ) : (
                            <ToggleLeft className="h-5 w-5 text-gray-400" />
                          )}
                          <span className="text-sm capitalize">
                            {getLinkStatus(link)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Event and Promotion */}
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Event</label>
                        <div className="text-sm text-gray-900 mt-1">
                          {getEventTitle(link)}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Promotion</label>
                        <div className="text-sm text-gray-900 mt-1">
                          {getPromotionTitle(link) || 'No promotion'}
                        </div>
                      </div>
                    </div>

                    {/* Promotion Code */}
                    {link.promotionCode && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Promotion Code</label>
                        <div className="text-sm font-mono bg-gray-100 p-2 rounded mt-1">
                          {link.promotionCode}
                        </div>
                      </div>
                    )}

                    {/* UTM Parameters */}
                    {link.utmParams && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">UTM Parameters</label>
                        <div className="mt-2 grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                          {link.utmParams.source && (
                            <div className="text-sm">
                              <span className="text-gray-600">Source:</span> {link.utmParams.source}
                            </div>
                          )}
                          {link.utmParams.medium && (
                            <div className="text-sm">
                              <span className="text-gray-600">Medium:</span> {link.utmParams.medium}
                            </div>
                          )}
                          {link.utmParams.campaign && (
                            <div className="text-sm">
                              <span className="text-gray-600">Campaign:</span> {link.utmParams.campaign}
                            </div>
                          )}
                          {link.utmParams.term && (
                            <div className="text-sm">
                              <span className="text-gray-600">Term:</span> {link.utmParams.term}
                            </div>
                          )}
                          {link.utmParams.content && (
                            <div className="text-sm">
                              <span className="text-gray-600">Content:</span> {link.utmParams.content}
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Target Link */}
                    {link.targetLink && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Target Link</label>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex-1 text-sm font-mono bg-gray-100 p-2 rounded break-all">
                            {link.targetLink}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCopyLink(link)}
                          >
                            {copiedLink === link.id ? 'Copied!' : 'Copy'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(link.targetLink, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="grid gap-4 md:grid-cols-2 text-sm text-gray-600 pt-4 border-t">
                      <div>
                        <span className="font-medium">Created:</span>{' '}
                        {new Date(link.createdAt).toLocaleString()}
                      </div>
                      <div>
                        <span className="font-medium">Updated:</span>{' '}
                        {new Date(link.updatedAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <Card className="shadow-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Link className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Links Found</h3>
            <p className="text-gray-600 text-center max-w-md mb-4">
              This affiliate user doesn't have any links created yet. 
              Create a new link to get started.
            </p>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create First Link
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default AffiliateLinksTab
