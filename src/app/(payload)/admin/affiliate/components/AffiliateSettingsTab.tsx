'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import type { User, AffiliateSetting } from '@/payload-types'
import { Settings, Plus, Edit, Eye, ToggleLeft, ToggleRight } from 'lucide-react'

interface Props {
  selectedUser: User
  userSettings: AffiliateSetting[]
}

const AffiliateSettingsTab: React.FC<Props> = ({
  selectedUser,
  userSettings,
}) => {
  const [expandedSetting, setExpandedSetting] = useState<number | null>(null)

  const toggleExpanded = (settingId: number) => {
    setExpandedSetting(expandedSetting === settingId ? null : settingId)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getEventTitle = (setting: AffiliateSetting) => {
    if (typeof setting.event === 'object' && setting.event?.title) {
      return setting.event.title
    }
    return 'Unknown Event'
  }

  const getPromotionTitles = (setting: AffiliateSetting) => {
    if (!setting.promotions || !Array.isArray(setting.promotions)) {
      return []
    }
    
    return setting.promotions.map(promo => {
      if (typeof promo === 'object' && promo?.title) {
        return promo.title
      }
      return 'Unknown Promotion'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Affiliate Settings</h3>
          <p className="text-sm text-gray-600">
            Manage affiliate program configurations for {selectedUser.email}
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add New Setting
        </Button>
      </div>

      {/* Settings List */}
      {userSettings.length > 0 ? (
        <div className="space-y-4">
          {userSettings.map((setting) => (
            <Card key={setting.id} className="shadow-md">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Settings className="h-5 w-5 text-gray-500" />
                    <div>
                      <CardTitle className="text-base">{setting.name}</CardTitle>
                      <CardDescription>
                        Event: {getEventTitle(setting)} • Created: {formatDate(setting.createdAt)}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={setting.isActive ? "default" : "secondary"}>
                      {setting.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(setting.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {expandedSetting === setting.id && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Basic Info */}
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status</label>
                        <div className="flex items-center gap-2 mt-1">
                          {setting.isActive ? (
                            <ToggleRight className="h-5 w-5 text-green-500" />
                          ) : (
                            <ToggleLeft className="h-5 w-5 text-gray-400" />
                          )}
                          <span className="text-sm">
                            {setting.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Event</label>
                        <div className="text-sm text-gray-900 mt-1">
                          {getEventTitle(setting)}
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    {setting.description && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description</label>
                        <div className="text-sm text-gray-900 mt-1 p-3 bg-gray-50 rounded">
                          {setting.description}
                        </div>
                      </div>
                    )}

                    {/* Promotions */}
                    <div>
                      <label className="text-sm font-medium text-gray-700">Associated Promotions</label>
                      <div className="mt-2">
                        {getPromotionTitles(setting).length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {getPromotionTitles(setting).map((title, index) => (
                              <Badge key={index} variant="outline">
                                {title}
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">No promotions configured</span>
                        )}
                      </div>
                    </div>

                    {/* Tiers */}
                    {setting.tiers && setting.tiers.length > 0 && (
                      <div>
                        <label className="text-sm font-medium text-gray-700">Affiliate Tiers</label>
                        <div className="mt-2 space-y-2">
                          {setting.tiers.map((tier, index) => (
                            <div key={index} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium">Tier {index + 1}</span>
                                <Badge variant="outline">
                                  {tier.minOrders || 0} - {tier.maxOrders || '∞'} orders
                                </Badge>
                              </div>
                              <div className="grid gap-2 md:grid-cols-2 text-sm">
                                <div>
                                  <span className="text-gray-600">Commission:</span>{' '}
                                  {tier.commissionPercentage || 0}%
                                </div>
                                <div>
                                  <span className="text-gray-600">Free Tickets:</span>{' '}
                                  {tier.freeTickets || 0}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Timestamps */}
                    <div className="grid gap-4 md:grid-cols-2 text-sm text-gray-600 pt-4 border-t">
                      <div>
                        <span className="font-medium">Created:</span>{' '}
                        {new Date(setting.createdAt).toLocaleString()}
                      </div>
                      <div>
                        <span className="font-medium">Updated:</span>{' '}
                        {new Date(setting.updatedAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <Card className="shadow-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Settings className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Settings Found</h3>
            <p className="text-gray-600 text-center max-w-md mb-4">
              This affiliate user doesn't have any settings configured yet. 
              Create a new setting to get started.
            </p>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create First Setting
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default AffiliateSettingsTab
