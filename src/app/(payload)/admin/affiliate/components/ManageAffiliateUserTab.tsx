'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { SelectInput } from '@payloadcms/ui'
import type { User, AffiliateLink, AffiliateSetting } from '@/payload-types'
import { UserChe<PERSON>, Settings, Link } from 'lucide-react'
import AffiliateSettingsTab from './AffiliateSettingsTab'
import AffiliateLinksTab from './AffiliateLinksTab'

interface Props {
  affiliateUsers: User[]
  affiliateSettings: AffiliateSetting[]
  affiliateLinks: AffiliateLink[]
  selectedAffiliateUser: User | null
  onSelectAffiliateUser: (user: User | null) => void
}

const ManageAffiliateUserTab: React.FC<Props> = ({
  affiliateUsers,
  affiliateSettings,
  affiliateLinks,
  selectedAffiliateUser,
  onSelectAffiliateUser,
}) => {
  const [activeNestedTab, setActiveNestedTab] = useState<'settings' | 'links'>('settings')

  // Prepare options for user selector
  const userOptions = [
    { label: 'Select an affiliate user...', value: '' },
    ...affiliateUsers.map(user => ({
      label: `${user.email} ${user.firstName && user.lastName ? `(${user.firstName} ${user.lastName})` : ''}`,
      value: user.id.toString(),
      user: user,
    }))
  ]

  const handleUserSelection = (option: any) => {
    if (!option || !option.value) {
      onSelectAffiliateUser(null)
      return
    }
    
    const selectedUser = affiliateUsers.find(user => user.id.toString() === option.value)
    onSelectAffiliateUser(selectedUser || null)
  }

  // Filter data for selected user
  const userSettings = selectedAffiliateUser 
    ? affiliateSettings.filter(setting => 
        typeof setting.affiliateUser === 'object' 
          ? setting.affiliateUser.id === selectedAffiliateUser.id
          : setting.affiliateUser === selectedAffiliateUser.id
      )
    : []

  const userLinks = selectedAffiliateUser 
    ? affiliateLinks.filter(link => 
        typeof link.affiliateUser === 'object' 
          ? link.affiliateUser.id === selectedAffiliateUser.id
          : link.affiliateUser === selectedAffiliateUser.id
      )
    : []

  return (
    <div className="space-y-6">
      {/* User Selection */}
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Step 1: Select Affiliate User
          </CardTitle>
          <CardDescription>
            Choose an affiliate user to manage their settings and links
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-w-md">
            <div className="field-type">
              <label className="field-label">Affiliate User</label>
              <SelectInput
                path="selectedUser"
                options={userOptions}
                value={selectedAffiliateUser?.id.toString() || ''}
                onChange={handleUserSelection}
                placeholder="Select an affiliate user..."
              />
            </div>
          </div>
          
          {selectedAffiliateUser && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Selected User Details</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Email:</span> {selectedAffiliateUser.email}
                </div>
                <div>
                  <span className="font-medium">Name:</span>{' '}
                  {selectedAffiliateUser.firstName && selectedAffiliateUser.lastName
                    ? `${selectedAffiliateUser.firstName} ${selectedAffiliateUser.lastName}`
                    : 'Not provided'}
                </div>
                <div>
                  <span className="font-medium">Phone:</span>{' '}
                  {selectedAffiliateUser.phoneNumber || 'Not provided'}
                </div>
                <div>
                  <span className="font-medium">Username:</span>{' '}
                  {selectedAffiliateUser.username || 'Not provided'}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 2: Nested Tabs */}
      {selectedAffiliateUser && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Step 2: Manage User Data</CardTitle>
            <CardDescription>
              Configure settings and manage links for {selectedAffiliateUser.email}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeNestedTab} onValueChange={(value) => setActiveNestedTab(value as 'settings' | 'links')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings ({userSettings.length})
                </TabsTrigger>
                <TabsTrigger value="links" className="flex items-center gap-2">
                  <Link className="h-4 w-4" />
                  Links ({userLinks.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="settings" className="mt-6">
                <AffiliateSettingsTab
                  selectedUser={selectedAffiliateUser}
                  userSettings={userSettings}
                />
              </TabsContent>

              <TabsContent value="links" className="mt-6">
                <AffiliateLinksTab
                  selectedUser={selectedAffiliateUser}
                  userLinks={userLinks}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* No User Selected State */}
      {!selectedAffiliateUser && (
        <Card className="shadow-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <UserCheck className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No User Selected</h3>
            <p className="text-gray-600 text-center max-w-md">
              Please select an affiliate user from the dropdown above to manage their settings and links.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default ManageAffiliateUserTab
