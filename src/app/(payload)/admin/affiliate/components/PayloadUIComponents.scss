@import '~@payloadcms/ui/scss';

// PayloadCMS Card Components
.payload-card {
  background: var(--theme-bg);
  border: 1px solid var(--theme-elevation-200);
  border-radius: var(--border-radius-s);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: var(--base);
  overflow: hidden;
}

.payload-card__header {
  padding: var(--base);
  border-bottom: 1px solid var(--theme-elevation-200);
  background: var(--theme-elevation-50);
}

.payload-card__content {
  padding: var(--base);
}

.payload-card__title {
  margin: 0 0 calc(var(--base) / 4) 0;
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text);
}

.payload-card__description {
  margin: 0;
  font-size: var(--font-size-small);
  color: var(--theme-elevation-600);
  line-height: var(--line-height-body);
}

// PayloadCMS Badge Components
.payload-badge {
  display: inline-flex;
  align-items: center;
  padding: calc(var(--base) / 8) calc(var(--base) / 4);
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-s);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payload-badge--default {
  background: var(--theme-success-100);
  color: var(--theme-success-600);
  border: 1px solid var(--theme-success-200);
}

.payload-badge--secondary {
  background: var(--theme-elevation-100);
  color: var(--theme-elevation-600);
  border: 1px solid var(--theme-elevation-200);
}

.payload-badge--success {
  background: var(--theme-success-100);
  color: var(--theme-success-600);
  border: 1px solid var(--theme-success-200);
}

.payload-badge--warning {
  background: var(--theme-warning-100);
  color: var(--theme-warning-600);
  border: 1px solid var(--theme-warning-200);
}

.payload-badge--danger {
  background: var(--theme-error-100);
  color: var(--theme-error-600);
  border: 1px solid var(--theme-error-200);
}

// PayloadCMS Tabs Components
.payload-tabs {
  width: 100%;
}

.payload-tabs__list {
  display: flex;
  border-bottom: 1px solid var(--theme-elevation-200);
  margin-bottom: var(--base);
}

.payload-tabs__trigger {
  background: none;
  border: none;
  padding: calc(var(--base) / 2) var(--base);
  font-size: var(--font-size-base);
  color: var(--theme-elevation-600);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: calc(var(--base) / 4);

  &:hover {
    color: var(--theme-text);
    background: var(--theme-elevation-50);
  }

  &--active {
    color: var(--theme-text);
    border-bottom-color: var(--theme-success-500);
    font-weight: var(--font-weight-medium);
  }
}

.payload-tabs__content {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// PayloadCMS Input Components
.payload-input {
  width: 100%;
  padding: calc(var(--base) / 2);
  border: 1px solid var(--theme-elevation-200);
  border-radius: var(--border-radius-s);
  font-size: var(--font-size-base);
  background: var(--theme-bg);
  color: var(--theme-text);
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--theme-success-500);
    box-shadow: 0 0 0 2px var(--theme-success-100);
  }

  &::placeholder {
    color: var(--theme-elevation-500);
  }
}

// PayloadCMS Table Components
.payload-table-wrapper {
  overflow-x: auto;
  border: 1px solid var(--theme-elevation-200);
  border-radius: var(--border-radius-s);
  background: var(--theme-bg);
}

.payload-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-small);
}

.payload-table__header {
  background: var(--theme-elevation-50);
}

.payload-table__head {
  padding: calc(var(--base) / 2);
  text-align: left;
  font-weight: var(--font-weight-medium);
  color: var(--theme-text);
  border-bottom: 1px solid var(--theme-elevation-200);
}

.payload-table__row {
  &:hover {
    background: var(--theme-elevation-25);
  }

  &:not(:last-child) {
    border-bottom: 1px solid var(--theme-elevation-100);
  }
}

.payload-table__cell {
  padding: calc(var(--base) / 2);
  color: var(--theme-text);
  vertical-align: top;
}

// PayloadCMS Grid Components
.payload-grid {
  display: grid;
  width: 100%;
}

.payload-grid--cols-1 {
  grid-template-columns: 1fr;
}

.payload-grid--cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.payload-grid--cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.payload-grid--cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.payload-grid--gap-sm {
  gap: calc(var(--base) / 2);
}

.payload-grid--gap-md {
  gap: var(--base);
}

.payload-grid--gap-lg {
  gap: calc(var(--base) * 1.5);
}

// Responsive adjustments
@include small-break {
  .payload-grid--cols-2,
  .payload-grid--cols-3,
  .payload-grid--cols-4 {
    grid-template-columns: 1fr;
  }

  .payload-tabs__list {
    flex-direction: column;
  }

  .payload-tabs__trigger {
    border-bottom: none;
    border-left: 2px solid transparent;

    &--active {
      border-left-color: var(--theme-success-500);
      border-bottom-color: transparent;
    }
  }
}

// Utility classes
.payload-flex {
  display: flex;
}

.payload-flex--center {
  align-items: center;
  justify-content: center;
}

.payload-flex--between {
  justify-content: space-between;
}

.payload-flex--gap {
  gap: calc(var(--base) / 2);
}

.payload-text--center {
  text-align: center;
}

.payload-text--muted {
  color: var(--theme-elevation-600);
}

.payload-text--small {
  font-size: var(--font-size-small);
}

.payload-text--bold {
  font-weight: var(--font-weight-bold);
}

.payload-mb {
  margin-bottom: var(--base);
}

.payload-mt {
  margin-top: var(--base);
}

.payload-p {
  padding: var(--base);
}

.payload-empty-state {
  text-align: center;
  padding: calc(var(--base) * 2);
  color: var(--theme-elevation-600);

  svg {
    width: 48px;
    height: 48px;
    margin-bottom: var(--base);
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 calc(var(--base) / 2) 0;
    font-size: var(--font-size-h5);
    color: var(--theme-text);
  }

  p {
    margin: 0 0 var(--base) 0;
    font-size: var(--font-size-small);
    line-height: var(--line-height-body);
  }
}
