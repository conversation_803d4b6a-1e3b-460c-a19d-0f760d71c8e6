'use client'

import React from 'react'
import type { User, AffiliateLink, AffiliateClickLog, AffiliateSetting } from '@/payload-types'
import { <PERSON>, Link, MousePointer, Settings, TrendingUp, DollarSign } from 'lucide-react'
import {
  PayloadCard,
  PayloadCardContent,
  PayloadCardDescription,
  PayloadCardHeader,
  PayloadCardTitle,
  PayloadGrid,
  PayloadGridItem
} from './PayloadUIComponents'

interface Props {
  affiliateUsers: User[]
  recentClickLogs: AffiliateClickLog[]
  affiliateSettings: AffiliateSetting[]
  affiliateLinks: AffiliateLink[]
}

const DashboardTab: React.FC<Props> = ({
  affiliateUsers,
  recentClickLogs,
  affiliateSettings,
  affiliateLinks,
}) => {
  // Calculate metrics
  const totalAffiliateUsers = affiliateUsers.length
  const totalAffiliateLinks = affiliateLinks.length
  const totalClicks = recentClickLogs.length
  const activeSettings = affiliateSettings.filter(setting => setting.isActive).length

  // Calculate recent activity (last 7 days)
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
  
  const recentClicks = recentClickLogs.filter(log => 
    new Date(log.createdAt) > sevenDaysAgo
  ).length

  const recentLinks = affiliateLinks.filter(link => 
    new Date(link.createdAt) > sevenDaysAgo
  ).length

  return (
    <div>
      {/* Overview Cards */}
      <PayloadGrid cols={4} gap="md">
        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <div className="payload-flex payload-flex--between">
                <PayloadCardTitle>Total Affiliate Users</PayloadCardTitle>
                <Users style={{ width: '16px', height: '16px', color: 'var(--theme-elevation-600)' }} />
              </div>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div style={{
                fontSize: 'var(--font-size-h3)',
                fontWeight: 'var(--font-weight-bold)',
                marginBottom: 'calc(var(--base) / 4)'
              }}>
                {totalAffiliateUsers}
              </div>
              <p style={{
                fontSize: 'var(--font-size-small)',
                color: 'var(--theme-elevation-600)',
                margin: 0
              }}>
                Active affiliate accounts
              </p>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>

        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <div className="payload-flex payload-flex--between">
                <PayloadCardTitle>Affiliate Links</PayloadCardTitle>
                <Link style={{ width: '16px', height: '16px', color: 'var(--theme-elevation-600)' }} />
              </div>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div style={{
                fontSize: 'var(--font-size-h3)',
                fontWeight: 'var(--font-weight-bold)',
                marginBottom: 'calc(var(--base) / 4)'
              }}>
                {totalAffiliateLinks}
              </div>
              <p style={{
                fontSize: 'var(--font-size-small)',
                color: 'var(--theme-elevation-600)',
                margin: 0
              }}>
                +{recentLinks} created this week
              </p>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>

        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <div className="payload-flex payload-flex--between">
                <PayloadCardTitle>Total Clicks</PayloadCardTitle>
                <MousePointer style={{ width: '16px', height: '16px', color: 'var(--theme-elevation-600)' }} />
              </div>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div style={{
                fontSize: 'var(--font-size-h3)',
                fontWeight: 'var(--font-weight-bold)',
                marginBottom: 'calc(var(--base) / 4)'
              }}>
                {totalClicks}
              </div>
              <p style={{
                fontSize: 'var(--font-size-small)',
                color: 'var(--theme-elevation-600)',
                margin: 0
              }}>
                {recentClicks} clicks this week
              </p>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>

        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <div className="payload-flex payload-flex--between">
                <PayloadCardTitle>Active Settings</PayloadCardTitle>
                <Settings style={{ width: '16px', height: '16px', color: 'var(--theme-elevation-600)' }} />
              </div>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div style={{
                fontSize: 'var(--font-size-h3)',
                fontWeight: 'var(--font-weight-bold)',
                marginBottom: 'calc(var(--base) / 4)'
              }}>
                {activeSettings}
              </div>
              <p style={{
                fontSize: 'var(--font-size-small)',
                color: 'var(--theme-elevation-600)',
                margin: 0
              }}>
                Configured affiliate programs
              </p>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>
      </PayloadGrid>

      {/* Recent Activity */}
      <PayloadGrid cols={2} gap="md" className="payload-mt">
        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <PayloadCardTitle>
                <div className="payload-flex payload-flex--gap">
                  <TrendingUp style={{ width: '20px', height: '20px' }} />
                  Recent Click Activity
                </div>
              </PayloadCardTitle>
              <PayloadCardDescription>Latest affiliate link clicks</PayloadCardDescription>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div>
                {recentClickLogs.slice(0, 5).map((log) => (
                  <div key={log.id} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    paddingBottom: 'calc(var(--base) / 2)',
                    marginBottom: 'calc(var(--base) / 2)',
                    borderBottom: '1px solid var(--theme-elevation-100)'
                  }}>
                    <div>
                      <p style={{
                        fontSize: 'var(--font-size-small)',
                        fontWeight: 'var(--font-weight-medium)',
                        margin: '0 0 calc(var(--base) / 4) 0'
                      }}>
                        {typeof log.affiliateUser === 'object' && log.affiliateUser?.email
                          ? log.affiliateUser.email
                          : 'Unknown User'}
                      </p>
                      <p style={{
                        fontSize: 'var(--font-size-small)',
                        color: 'var(--theme-elevation-600)',
                        margin: 0
                      }}>
                        {log.ip && `IP: ${log.ip}`}
                        {log.location && ` • ${log.location}`}
                      </p>
                    </div>
                    <div style={{
                      fontSize: 'var(--font-size-small)',
                      color: 'var(--theme-elevation-600)'
                    }}>
                      {new Date(log.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
                {recentClickLogs.length === 0 && (
                  <p style={{
                    fontSize: 'var(--font-size-small)',
                    color: 'var(--theme-elevation-600)',
                    margin: 0
                  }}>
                    No recent clicks
                  </p>
                )}
              </div>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>

        <PayloadGridItem>
          <PayloadCard>
            <PayloadCardHeader>
              <PayloadCardTitle>
                <div className="payload-flex payload-flex--gap">
                  <DollarSign style={{ width: '20px', height: '20px' }} />
                  Top Performing Affiliates
                </div>
              </PayloadCardTitle>
              <PayloadCardDescription>Based on click activity</PayloadCardDescription>
            </PayloadCardHeader>
            <PayloadCardContent>
              <div>
                {affiliateUsers.slice(0, 5).map((user) => {
                  const userClicks = recentClickLogs.filter(log =>
                    typeof log.affiliateUser === 'object' && log.affiliateUser?.id === user.id
                  ).length

                  return (
                    <div key={user.id} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      paddingBottom: 'calc(var(--base) / 2)',
                      marginBottom: 'calc(var(--base) / 2)',
                      borderBottom: '1px solid var(--theme-elevation-100)'
                    }}>
                      <div>
                        <p style={{
                          fontSize: 'var(--font-size-small)',
                          fontWeight: 'var(--font-weight-medium)',
                          margin: '0 0 calc(var(--base) / 4) 0'
                        }}>
                          {user.email}
                        </p>
                        <p style={{
                          fontSize: 'var(--font-size-small)',
                          color: 'var(--theme-elevation-600)',
                          margin: 0
                        }}>
                          {user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : 'No name provided'}
                        </p>
                      </div>
                      <div style={{
                        fontSize: 'var(--font-size-small)',
                        fontWeight: 'var(--font-weight-medium)'
                      }}>
                        {userClicks} clicks
                      </div>
                    </div>
                  )
                })}
                {affiliateUsers.length === 0 && (
                  <p style={{
                    fontSize: 'var(--font-size-small)',
                    color: 'var(--theme-elevation-600)',
                    margin: 0
                  }}>
                    No affiliate users
                  </p>
                )}
              </div>
            </PayloadCardContent>
          </PayloadCard>
        </PayloadGridItem>
      </PayloadGrid>
    </div>
  )
}

export default DashboardTab
