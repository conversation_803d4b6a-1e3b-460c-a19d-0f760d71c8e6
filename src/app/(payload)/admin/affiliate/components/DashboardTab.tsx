'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import type { User, AffiliateLink, AffiliateClickLog, AffiliateSetting } from '@/payload-types'
import { <PERSON>, Link, MousePointer, Settings, TrendingUp, DollarSign } from 'lucide-react'

interface Props {
  affiliateUsers: User[]
  recentClickLogs: AffiliateClickLog[]
  affiliateSettings: AffiliateSetting[]
  affiliateLinks: AffiliateLink[]
}

const DashboardTab: React.FC<Props> = ({
  affiliateUsers,
  recentClickLogs,
  affiliateSettings,
  affiliateLinks,
}) => {
  // Calculate metrics
  const totalAffiliateUsers = affiliateUsers.length
  const totalAffiliateLinks = affiliateLinks.length
  const totalClicks = recentClickLogs.length
  const activeSettings = affiliateSettings.filter(setting => setting.isActive).length

  // Calculate recent activity (last 7 days)
  const sevenDaysAgo = new Date()
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
  
  const recentClicks = recentClickLogs.filter(log => 
    new Date(log.createdAt) > sevenDaysAgo
  ).length

  const recentLinks = affiliateLinks.filter(link => 
    new Date(link.createdAt) > sevenDaysAgo
  ).length

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Affiliate Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAffiliateUsers}</div>
            <p className="text-xs text-muted-foreground">
              Active affiliate accounts
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Affiliate Links</CardTitle>
            <Link className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalAffiliateLinks}</div>
            <p className="text-xs text-muted-foreground">
              +{recentLinks} created this week
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
            <MousePointer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalClicks}</div>
            <p className="text-xs text-muted-foreground">
              {recentClicks} clicks this week
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Settings</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSettings}</div>
            <p className="text-xs text-muted-foreground">
              Configured affiliate programs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Click Activity
            </CardTitle>
            <CardDescription>Latest affiliate link clicks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentClickLogs.slice(0, 5).map((log) => (
                <div key={log.id} className="flex items-center justify-between border-b pb-2">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">
                      {typeof log.affiliateUser === 'object' && log.affiliateUser?.email 
                        ? log.affiliateUser.email 
                        : 'Unknown User'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {log.ip && `IP: ${log.ip}`}
                      {log.location && ` • ${log.location}`}
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(log.createdAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
              {recentClickLogs.length === 0 && (
                <p className="text-sm text-muted-foreground">No recent clicks</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Top Performing Affiliates
            </CardTitle>
            <CardDescription>Based on click activity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {affiliateUsers.slice(0, 5).map((user) => {
                const userClicks = recentClickLogs.filter(log => 
                  typeof log.affiliateUser === 'object' && log.affiliateUser?.id === user.id
                ).length

                return (
                  <div key={user.id} className="flex items-center justify-between border-b pb-2">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{user.email}</p>
                      <p className="text-xs text-muted-foreground">
                        {user.firstName && user.lastName 
                          ? `${user.firstName} ${user.lastName}` 
                          : 'No name provided'}
                      </p>
                    </div>
                    <div className="text-sm font-medium">
                      {userClicks} clicks
                    </div>
                  </div>
                )
              })}
              {affiliateUsers.length === 0 && (
                <p className="text-sm text-muted-foreground">No affiliate users</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default DashboardTab
